import React, { useState, useRef } from "react";
import Spline from "@splinetool/react-spline";
import "./index.less";

function LoadingSpinner() {
  return <div className="loading-spinner">加载中...</div>;
}

export default function SplineWithModal() {
  const [activeIcon, setActiveIcon] = useState(null);
  const [loading, setLoading] = useState(true);

  const spline = useRef();

  // 监听 Spline 模块的点击事件
  function onLoad(splineApp) {
    setLoading(false);
    spline.current = splineApp;

    splineApp.addEventListener("mouseDown", (e) => {
      const clickedObject = e?.target;
      // if (!clickedObject || !clickedObject.name) return;
      const psKey = clickedObject.name;
      // 如果点击的是目标之一，就打开 modal
      console.log("e :>> ", e);
      requestAnimationFrame(() => {

      });
    });
  }

  function triggerAnimation() {
    const modelAlarm = spline.current.findObjectByName("YC01");
    const modelNormal = spline.current.findObjectByName("GJ01");
    console.log('--->', modelAlarm)
    modelNormal.hide();
    modelAlarm.show();
  }

  return (
    <div className="width-height-100 relative">
      {loading && <LoadingSpinner />}
      {/* 渲染 Spline 场景 */}
      <Spline
        scene="https://prod.spline.design/LIS8Z88npa62VZXP/scene.splinecode"
        onLoad={onLoad}
      />
    </div>
  );
}
