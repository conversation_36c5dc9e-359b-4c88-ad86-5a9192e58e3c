/* CSS Reset - 更全面的样式重置 */

/* 1. 基础重置 */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* 2. HTML 和 Body */
html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #ffffff;
    color: #333;
    min-height: 100vh;
}

/* 3. Root 容器 */
#root {
    height: 100vh;
    width: 100vw;
    overflow-x: hidden;
}

/* 4. 标题元素 */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    font-weight: normal;
    line-height: 1.2;
}

/* 5. 段落和文本 */
p {
    margin: 0;
    padding: 0;
}

/* 6. 列表 */
ul, ol, li {
    margin: 0;
    padding: 0;
    list-style: none;
}

/* 7. 链接 */
a {
    color: inherit;
    text-decoration: none;
    background-color: transparent;
}

a:hover {
    text-decoration: none;
}

/* 8. 图片和媒体 */
img {
    max-width: 100%;
    height: auto;
    border: 0;
    vertical-align: middle;
}

svg {
    vertical-align: middle;
}

/* 9. 表单元素 */
button,
input,
optgroup,
select,
textarea {
    margin: 0;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    border: none;
    background: none;
    outline: none;
}

button {
    cursor: pointer;
    background-color: transparent;
    border: none;
    outline: none;
}

button:disabled {
    cursor: not-allowed;
}

input[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px;
}

input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

/* 10. 表格 */
table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}

th, td {
    padding: 0;
    text-align: left;
}

/* 11. 其他元素 */
blockquote {
    margin: 0;
    padding: 0;
}

code,
kbd,
pre,
samp {
    font-family: 'Courier New', Courier, monospace;
    font-size: 1em;
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
    border: 0;
    border-top: 1px solid #eee;
    margin: 0;
}

/* 12. 隐藏元素 */
[hidden] {
    display: none !important;
}

/* 13. 可访问性 */
:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* 14. 滚动条样式 (Webkit) */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 15. 选择文本样式 */
::selection {
    background-color: #007bff;
    color: #ffffff;
}

::-moz-selection {
    background-color: #007bff;
    color: #ffffff;
}

