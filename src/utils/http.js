import axios, { AxiosError } from "axios";
import storage from "@/utils/localStorage";

//const userStore = useUserStoreWithOut();
// 数据返回的接口
// 定义请求响应参数，不含data

const CanCelToken = axios.CancelToken;
const source = CanCelToken.source();
// 请求响应参数，包含payload

const URL = import.meta.env.VITE_GLOB_API_URL;
const RequestEnums = {
    TIMEOUT: 20000,
    OVERDUE: 600, // 登录失效
    FAIL: 999, // 请求失败
    SUCCESS: 200, // 请求成功
};
const config = {
    // 默认地址
    baseURL: URL,
    // 设置超时时间
    timeout: RequestEnums.TIMEOUT,
    // // 跨域时候允许携带凭证
    // withCredentials: true,
};

class RequestHttp {
    // 定义成员变量并指定类型
    constructor(config) {
        // 实例化axios
        this.service = axios.create(config);
        /**
         * 请求拦截器
         * 客户端发送请求 -> [请求拦截器] -> 服务器
         * token校验(JWT) : 接受服务器返回的token,存储到vuex/pinia/本地储存当中
         */
        this.service.interceptors.request.use(
            (config) => {
                let token = storage.get('token') || ''
                let tenantId = storage.get('tenantId')
                return {
                    ...config,
                    headers: {
                        "X-Access-Token": token, // 请求头中携带token信息
                        "client-type": "app",
                        Authorization: "Bearer " + token,
                        "device-id": storage.get("deviceId"),
                        tenant_id: tenantId || "1",
                    },
                };
            },
            (error) => {
                // 请求报错
                Promise.reject(error);
            }
        );

        /**
         * 响应拦截器
         * 服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
         */

        this.service.interceptors.response.use(
            (response) => {
                const { data, config } = response; // 解构
                const hasErrorMsg = config.data
                    ? Object.prototype.hasOwnProperty.call(
                        JSON.parse(config.data),
                        "errorMsg"
                    )
                    : false;

                // 全局错误信息拦截（防止下载文件得时候返回数据流，没有code，直接报错）
                if (
                    !(
                        response.status == 200 &&
                        (data.result_code == "1" ||
                            data.result_code == "2" ||
                            data.success ||
                            data.repCode == "0000" ||
                            data.code === 0)
                    )
                ) {
                    const msg =
                        data.message || data.result_message || data.msg || data.result_msg;
                    if(!msg){
                        return data
                    }
                    if (!hasErrorMsg) {
                        Tips.message(msg); // 此处也可以使用组件提示报错信息
                    }

                    throw new Error('Error => ' + msg) // 接口报出错误信息后 扔出错误阻断后续代码
                    return Promise.reject(data);
                }
                return data;
            },
            (error) => {
                const { response } = error;
                if (response) {
                    this.handleCode(response.status);
                }
                if (!window.navigator.onLine) {
                    Tips.message("网络连接失败");
                }
            }
        );
    }
    handleCode(code) {
        switch (code) {
            case 404:
                Tips.message("请求资源不存在");
                break;
            case 401:
                Tips.message("用户信息已过期，请重新扫码！");
                break;
            default:
                Tips.message("请求失败");
                break;
        }
    }

    // 常用方法封装
    get(url, params) {
        return this.service.get(url, { params });
    }
    post(url, params) {
        return this.service.post(url, params);
    }
    put(url, params) {
        return this.service.put(url, params);
    }
    delete(url, params) {
        return this.service.delete(url, { params });
    }
}

// 导出一个实例对象
export default new RequestHttp(config);
