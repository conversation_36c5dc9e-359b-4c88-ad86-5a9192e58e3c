/* localstorage封装，缓存对象 */

/** key前缀 */
const keyPrefix = "pro__";

interface StorageInterface {
    /**
     * 设置localStorage
     * @param value 内容
     * @param expires 有效时间
     */
    set: (key: string, value: any, expires?: number) => void;
    /** 获取localStorage,会自动转json */
    get: (key: string) => any;
    /** 是否含有key */
    has: (key: string) => boolean;
    /** 移除 */
    remove: (key: string) => void;
    /** 移除全部缓存 */
    clear: () => void;
    /** 移除自己前缀的全部缓存 */
    clearSelf: () => void;
}

const storage: StorageInterface = {
    set: () => {},
    get: () => "",
    has: () => false,
    remove: () => {},
    clear: () => {},
    clearSelf: () => {},
};

/**
 * 是否过期
 */
const isFresh = (valObj: valObjParams) => {
    const now = new Date().getTime();
    return valObj.addTime + valObj.expires >= now;
};

/* 给key值添加前缀 */
const addPrefix = (key: string) => {
    return `${keyPrefix}${key}`;
};

/* 加方法 */
const extend = (s: Storage) => {
    return {
        set(key: string, value: any, expire?: number) {
        const skey = addPrefix(key);
        s.setItem(
            skey,
            JSON.stringify({
                value: value,
                expire: expire ? `${new Date().getTime()}+${expire}` : null,
            })
        );

        if (value === undefined || value === null) {
            s.removeItem(skey);
        }
    },
    get(key: string) {
        const skey = addPrefix(key);
        const item = JSON.parse(s.getItem(skey) as any);
        // const def =
        //   arguments.length > 1 && arguments[1] !== undefined
        //     ? arguments[1]
        //     : null;
        const def = item != null ? item.value : null;
        // 说明设置了失效时间
        // if (item !==null) {
        //   if (isFresh(item)) {
        //     return item.value;
        //   }
        //   /* 缓存过期，清除缓存，返回null */
        //   s.removeItem(skey);
        //   return null;
        // }
        if (item !== null) {
            try {
                const data = JSON.parse(item);

                if (data.expire === null) {
                    return data.value;
                }

                if (data.expire >= new Date().getTime()) {
                    return data.value;
                }

                this.remove(skey);
            } catch (err) {
                return def;
            }
        }

        return def;
    },
    has(key: string) {
        const skey = addPrefix(key);
        return !!s.getItem(skey);
    },
    remove: (key: string) => {
        const skey = addPrefix(key);
        s.removeItem(skey);
    },
        clear: () => {
        s.clear();
    },
        clearSelf: () => {
        const arr = Array.from({ length: s.length }, (_, i) => s.key(i)).filter(
            (str) => str?.startsWith(keyPrefix)
        );
        arr.forEach((str) => s.removeItem(str as string));
    },
};
};

Object.assign(storage, extend(window.localStorage));

export default storage;
