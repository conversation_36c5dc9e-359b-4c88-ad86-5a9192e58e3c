/* localstorage封装，缓存对象 */

/** key前缀 */
const keyPrefix = "pro__";

const storage = {
    set: () => {},
    get: () => "",
    has: () => false,
    remove: () => {},
    clear: () => {},
    clearSelf: () => {},
};

/**
 * 是否过期
 */
const isFresh = (valObj) => {
    const now = new Date().getTime();
    return valObj.addTime + valObj.expires >= now;
};

/* 给key值添加前缀 */
const addPrefix = (key) => {
    return `${keyPrefix}${key}`;
};

/* 加方法 */
const extend = (s) => {
    return {
        set(key, value, expire) {
            const skey = addPrefix(key);
            s.setItem(
                skey,
                JSON.stringify({
                    value: value,
                    expire: expire ? `${new Date().getTime()}+${expire}` : null,
                })
            );

            if (value === undefined || value === null) {
                s.removeItem(skey);
            }
        },
        get(key) {
            const skey = addPrefix(key);
            const item = s.getItem(skey);
            // const def =
            //   arguments.length > 1 && arguments[1] !== undefined
            //     ? arguments[1]
            //     : null;
            const def = item != null ? JSON.parse(item).value : null;
            // 说明设置了失效时间
            // if (item !==null) {
            //   if (isFresh(item)) {
            //     return item.value;
            //   }
            //   /* 缓存过期，清除缓存，返回null */
            //   s.removeItem(skey);
            //   return null;
            // }
            if (item !== null) {
                try {
                    const data = JSON.parse(item);

                    if (data.expire === null) {
                        return data.value;
                    }

                    if (data.expire >= new Date().getTime()) {
                        return data.value;
                    }

                    this.remove(skey);
                } catch (err) {
                    return def;
                }
            }

            return def;
        },
        has(key) {
            const skey = addPrefix(key);
            return !!s.getItem(skey);
        },
        remove: (key) => {
            const skey = addPrefix(key);
            s.removeItem(skey);
        },
        clear: () => {
            s.clear();
        },
        clearSelf: () => {
            const arr = Array.from({ length: s.length }, (_, i) => s.key(i)).filter(
                (str) => str?.startsWith(keyPrefix)
            );
            arr.forEach((str) => s.removeItem(str));
        },
    };
};

Object.assign(storage, extend(window.localStorage));

export default storage;
