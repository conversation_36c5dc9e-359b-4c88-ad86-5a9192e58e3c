import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from "node:url";
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, __dirname);
    console.log('env--->', env)
    return {
        plugins: [react()],
        base: env === 'production' ? '/splineModel/' :'/',
        resolve: {
            alias: {
                "@": fileURLToPath(new URL("./src", import.meta.url)),
            },
        },
    };
});