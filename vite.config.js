import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from "node:url";
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
    console.log('mode--->', mode);
    return {
        plugins: [react()],
        base: mode === 'pro' ? '/splineModel/' : '/',
        resolve: {
            alias: {
                "@": fileURLToPath(new URL("./src", import.meta.url)),
            },
        },
    };
});