import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from "node:url";
const isProduction = process.env.NODE_ENV === 'production';
// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: isProduction ? '/splineModel/' :'/',
  public: isProduction ? '/splineModel/public' : '/public',
  publicDir: isProduction ? '/splineModel/public' : '/public',
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
})
